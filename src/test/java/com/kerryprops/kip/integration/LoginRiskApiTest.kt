package com.kerryprops.kip.integration

import com.kerryprops.kip.crypto.RsaCryptoController
import com.kerryprops.kip.crypto.RsaCryptoController.PublicKeyResponse
import com.kerryprops.kip.crypto.RsaCryptoService
import io.restassured.RestAssured
import jakarta.annotation.Resource
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus

/**
 * LoginRiskApiTest.
 *
 */
internal class LoginRiskApiTest : BaseIntegrationTest() {

    @Test
    @DisplayName("非大陆IP-允许登录")
    fun nonMainlandIp_allowLogin() {
    }
}