package com.kerryprops.kip.integration;

import com.kerryprops.kip.riskcontrol.login.LoginRiskAssessRequest;
import com.kerryprops.kip.riskcontrol.login.LoginRiskAssessResponse;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.TestPropertySource;

import static io.restassured.RestAssured.given;
import static org.assertj.core.api.Assertions.assertThat;

/**
 * Simple integration test to understand the actual behavior.
 */
@TestPropertySource(properties = {
    "drools.enabled=true",
    "logging.level.com.kerryprops.kip.riskcontrol=DEBUG",
    "phone.virtual-prefixes.china-mobile[0]=165",
    "phone.virtual-prefixes.china-mobile[1]=1703",
    "phone.virtual-prefixes.china-mobile[2]=1705",
    "phone.virtual-prefixes.china-mobile[3]=1706",
    "phone.virtual-prefixes.china-unicom[0]=167",
    "phone.virtual-prefixes.china-unicom[1]=1704",
    "phone.virtual-prefixes.china-unicom[2]=1707",
    "phone.virtual-prefixes.china-unicom[3]=1708",
    "phone.virtual-prefixes.china-unicom[4]=1709",
    "phone.virtual-prefixes.china-unicom[5]=171",
    "phone.virtual-prefixes.china-telecom[0]=162",
    "phone.virtual-prefixes.china-telecom[1]=1700",
    "phone.virtual-prefixes.china-telecom[2]=1701",
    "phone.virtual-prefixes.china-telecom[3]=1702",
    "phone.virtual-prefixes.china-broadcast[0]=192"
})
@DisplayName("Simple Login Risk Assessment Integration Test")
class SimpleLoginRiskAssessmentTest extends BaseIntegrationTest {

    private static final String LOGIN_ASSESSMENT_ENDPOINT = "/risk/login-assessment";

    @Test
    @DisplayName("Should return response for basic request")
    void shouldReturnResponseForBasicRequest() {
        // Given
        LoginRiskAssessRequest request = new LoginRiskAssessRequest();
        request.setMallCode("ALL");
        request.setIpAddress("************");
        request.setPhoneNumber("13800138000");
        request.setUnionId("test-union-id");
        request.setTxRiskLevel("低");
        request.setUnionIdLoginCount(0);
        request.setPhoneLoginCount(0);
        request.setMemberPoints(1000);
        request.setIsFrozenMember(false);

        // When
        LoginRiskAssessResponse response = given()
                .contentType(ContentType.JSON)
                .body(request)
                .when()
                .post(LOGIN_ASSESSMENT_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .extract()
                .as(LoginRiskAssessResponse.class);

        // Then - Log actual response for debugging
        System.out.println("=== ACTUAL RESPONSE ===");
        System.out.println("Mall Code: " + response.getMallCode());
        System.out.println("Risk Result: " + response.getRiskResult());
        System.out.println("Block Message: " + response.getBlockMessage());
        System.out.println("Assessment Details: " + response.getAssessmentDetails());
        System.out.println("IP Geolocation: " + response.getIpGeolocation());
        System.out.println("=======================");

        // Verify basic response structure
        assertThat(response).isNotNull();
        assertThat(response.getRiskResult()).isNotNull();
        assertThat(response.getAssessmentTime()).isNotNull();
    }

    @Test
    @DisplayName("Should return response for virtual phone number")
    void shouldReturnResponseForVirtualPhoneNumber() {
        // Given
        LoginRiskAssessRequest request = new LoginRiskAssessRequest();
        request.setMallCode("ALL");
        request.setIpAddress("************");
        request.setPhoneNumber("17012345678"); // Virtual number
        request.setUnionId("test-union-id");
        request.setTxRiskLevel("低");
        request.setUnionIdLoginCount(0);
        request.setPhoneLoginCount(0);
        request.setMemberPoints(1000);
        request.setIsFrozenMember(false);

        // When
        LoginRiskAssessResponse response = given()
                .contentType(ContentType.JSON)
                .body(request)
                .when()
                .post(LOGIN_ASSESSMENT_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .extract()
                .as(LoginRiskAssessResponse.class);

        // Then - Log actual response for debugging
        System.out.println("=== VIRTUAL PHONE RESPONSE ===");
        System.out.println("Mall Code: " + response.getMallCode());
        System.out.println("Risk Result: " + response.getRiskResult());
        System.out.println("Block Message: " + response.getBlockMessage());
        System.out.println("Assessment Details: " + response.getAssessmentDetails());
        System.out.println("IP Geolocation: " + response.getIpGeolocation());
        System.out.println("===============================");

        // Verify basic response structure
        assertThat(response).isNotNull();
        assertThat(response.getRiskResult()).isNotNull();
        assertThat(response.getAssessmentTime()).isNotNull();
    }

    @Test
    @DisplayName("Should return response for frozen member")
    void shouldReturnResponseForFrozenMember() {
        // Given
        LoginRiskAssessRequest request = new LoginRiskAssessRequest();
        request.setMallCode("ALL");
        request.setIpAddress("************");
        request.setPhoneNumber("13800138000");
        request.setUnionId("test-union-id");
        request.setTxRiskLevel("低");
        request.setUnionIdLoginCount(0);
        request.setPhoneLoginCount(0);
        request.setMemberPoints(1000);
        request.setIsFrozenMember(true); // Frozen member

        // When
        LoginRiskAssessResponse response = given()
                .contentType(ContentType.JSON)
                .body(request)
                .when()
                .post(LOGIN_ASSESSMENT_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .extract()
                .as(LoginRiskAssessResponse.class);

        // Then - Log actual response for debugging
        System.out.println("=== FROZEN MEMBER RESPONSE ===");
        System.out.println("Mall Code: " + response.getMallCode());
        System.out.println("Risk Result: " + response.getRiskResult());
        System.out.println("Block Message: " + response.getBlockMessage());
        System.out.println("Assessment Details: " + response.getAssessmentDetails());
        System.out.println("IP Geolocation: " + response.getIpGeolocation());
        System.out.println("===============================");

        // Verify basic response structure
        assertThat(response).isNotNull();
        assertThat(response.getRiskResult()).isNotNull();
        assertThat(response.getAssessmentTime()).isNotNull();
    }

    @Test
    @DisplayName("Should return response for high risk level")
    void shouldReturnResponseForHighRiskLevel() {
        // Given
        LoginRiskAssessRequest request = new LoginRiskAssessRequest();
        request.setMallCode("ALL");
        request.setIpAddress("************");
        request.setPhoneNumber("13800138000");
        request.setUnionId("test-union-id");
        request.setTxRiskLevel("高"); // High risk
        request.setUnionIdLoginCount(0);
        request.setPhoneLoginCount(0);
        request.setMemberPoints(1000);
        request.setIsFrozenMember(false);

        // When
        LoginRiskAssessResponse response = given()
                .contentType(ContentType.JSON)
                .body(request)
                .when()
                .post(LOGIN_ASSESSMENT_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .extract()
                .as(LoginRiskAssessResponse.class);

        // Then - Log actual response for debugging
        System.out.println("=== HIGH RISK RESPONSE ===");
        System.out.println("Mall Code: " + response.getMallCode());
        System.out.println("Risk Result: " + response.getRiskResult());
        System.out.println("Block Message: " + response.getBlockMessage());
        System.out.println("Assessment Details: " + response.getAssessmentDetails());
        System.out.println("IP Geolocation: " + response.getIpGeolocation());
        System.out.println("===========================");

        // Verify basic response structure
        assertThat(response).isNotNull();
        assertThat(response.getRiskResult()).isNotNull();
        assertThat(response.getAssessmentTime()).isNotNull();
    }

    @Test
    @DisplayName("Should return response for high UnionID login count")
    void shouldReturnResponseForHighUnionIdLoginCount() {
        // Given
        LoginRiskAssessRequest request = new LoginRiskAssessRequest();
        request.setMallCode("ALL");
        request.setIpAddress("************");
        request.setPhoneNumber("13800138000");
        request.setUnionId("test-union-id");
        request.setTxRiskLevel("低");
        request.setUnionIdLoginCount(10); // High login count
        request.setPhoneLoginCount(0);
        request.setMemberPoints(1000);
        request.setIsFrozenMember(false);

        // When
        LoginRiskAssessResponse response = given()
                .contentType(ContentType.JSON)
                .body(request)
                .when()
                .post(LOGIN_ASSESSMENT_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .extract()
                .as(LoginRiskAssessResponse.class);

        // Then - Log actual response for debugging
        System.out.println("=== HIGH LOGIN COUNT RESPONSE ===");
        System.out.println("Mall Code: " + response.getMallCode());
        System.out.println("Risk Result: " + response.getRiskResult());
        System.out.println("Block Message: " + response.getBlockMessage());
        System.out.println("Assessment Details: " + response.getAssessmentDetails());
        System.out.println("IP Geolocation: " + response.getIpGeolocation());
        System.out.println("===================================");

        // Verify basic response structure
        assertThat(response).isNotNull();
        assertThat(response.getRiskResult()).isNotNull();
        assertThat(response.getAssessmentTime()).isNotNull();
    }
}
