package com.kerryprops.kip.riskcontrol.login;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.riskcontrol.RiskAssessmentController;
import com.kerryprops.kip.riskcontrol.general.RiskResult;
import com.kerryprops.kip.riskcontrol.shared.IpGeolocationInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.LocalDateTime;
import java.util.function.Function;


import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Comprehensive test cases for login risk assessment endpoint.
 * Tests all login scenario risk control rules defined in login-risk-assessment.drl.
 *
 * <p>Following TDD principles and functional programming patterns.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Login Risk Assessment Controller Tests")
class LoginRiskAssessmentControllerTest {

    @Mock
    private LoginRiskAssessmentService loginRiskAssessmentService;

    @InjectMocks
    private RiskAssessmentController controller;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
        objectMapper = new ObjectMapper();
    }

    // ========== Test Data Builders (Functional Programming Approach) ==========

    /**
     * Creates a base login risk assessment request builder.
     */
    private Function<String, LoginRiskAssessRequest> createBaseRequest = mallCode -> {
        LoginRiskAssessRequest request = new LoginRiskAssessRequest();
        request.setMallCode(mallCode);
        request.setIpAddress("************");
        request.setPhoneNumber("13800138000");
        request.setUnionId("test-union-id");
        request.setTxRiskLevel("低");
        request.setUnionIdLoginCount(0);
        request.setPhoneLoginCount(0);
        request.setMemberPoints(1000);
        request.setIsFrozenMember(false);
        return request;
    };

    /**
     * Creates a response builder for rejected scenarios.
     */
    private Function<String, LoginRiskAssessResponse> createRejectedResponse = blockMessage -> {
        LoginRiskAssessResponse response = new LoginRiskAssessResponse();
        response.setMallCode("ALL");
        response.setRiskResult(RiskResult.REJECT);
        response.setBlockMessage(blockMessage);
        response.setAssessmentTime(LocalDateTime.now());
        return response;
    };

    /**
     * Creates a response builder for passed scenarios.
     */
    private Function<String, LoginRiskAssessResponse> createPassedResponse = details -> {
        LoginRiskAssessResponse response = new LoginRiskAssessResponse();
        response.setMallCode("ALL");
        response.setRiskResult(RiskResult.PASS);
        response.setAssessmentDetails(details);
        response.setAssessmentTime(LocalDateTime.now());
        return response;
    };

    // ========== Phone Number Validation Rule Tests ==========

    @Nested
    @DisplayName("Phone Number Validation Rules")
    class PhoneNumberValidationTests {

        @Test
        @DisplayName("Should block virtual carrier numbers with error code 40001 (silent blocking)")
        void shouldBlockVirtualCarrierNumbers() throws Exception {
            // Given - Virtual phone number request
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setPhoneNumber("17012345678"); // Virtual number

            LoginRiskAssessResponse expectedResponse = createRejectedResponse.apply("无提示，收不到验证码");
            expectedResponse.setAssessmentDetails("登录：虚拟号段被拦截");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("REJECT"))
                    .andExpect(jsonPath("$.blockMessage").value("无提示，收不到验证码"))
                    .andExpect(jsonPath("$.assessmentDetails").value("登录：虚拟号段被拦截"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }

        @ParameterizedTest
        @DisplayName("Should block various virtual carrier number prefixes")
        @ValueSource(strings = {"170", "171", "162", "165", "167"})
        void shouldBlockVariousVirtualCarrierPrefixes(String prefix) throws Exception {
            // Given
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setPhoneNumber(prefix + "12345678");

            LoginRiskAssessResponse expectedResponse = createRejectedResponse.apply("无提示，收不到验证码");
            expectedResponse.setAssessmentDetails("登录：虚拟号段被拦截");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("REJECT"))
                    .andExpect(jsonPath("$.blockMessage").value("无提示，收不到验证码"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }

        @Test
        @DisplayName("Should allow normal phone numbers")
        void shouldAllowNormalPhoneNumbers() throws Exception {
            // Given - Normal phone number
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setPhoneNumber("13800138000"); // Normal number

            LoginRiskAssessResponse expectedResponse = createPassedResponse.apply("大陆IP默认通过");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("PASS"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }
    }

    // ========== ID Validation Rule Tests ==========

    @Nested
    @DisplayName("ID Validation Rules")
    class IdValidationTests {

        @Test
        @DisplayName("Should block when UnionID login count exceeds 5 with error code 40002")
        void shouldBlockWhenUnionIdLoginCountExceeds5() throws Exception {
            // Given - UnionID with >5 logins
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setUnionIdLoginCount(6);

            LoginRiskAssessResponse expectedResponse = createRejectedResponse
                    .apply("此微信登录会员账户已超过限额，您可登录近期使用过的会员账户");
            expectedResponse.setAssessmentDetails("登录：ID校验超限被拦截");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("REJECT"))
                    .andExpect(jsonPath("$.blockMessage").value("此微信登录会员账户已超过限额，您可登录近期使用过的会员账户"))
                    .andExpect(jsonPath("$.assessmentDetails").value("登录：ID校验超限被拦截"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }

        @ParameterizedTest
        @DisplayName("Should block for various UnionID login counts above threshold")
        @ValueSource(ints = {6, 10, 15, 100})
        void shouldBlockForVariousUnionIdLoginCountsAboveThreshold(int loginCount) throws Exception {
            // Given
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setUnionIdLoginCount(loginCount);

            LoginRiskAssessResponse expectedResponse = createRejectedResponse
                    .apply("此微信登录会员账户已超过限额，您可登录近期使用过的会员账户");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("REJECT"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }

        @ParameterizedTest
        @DisplayName("Should allow UnionID login counts at or below threshold")
        @ValueSource(ints = {0, 1, 3, 5})
        void shouldAllowUnionIdLoginCountsAtOrBelowThreshold(int loginCount) throws Exception {
            // Given
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setUnionIdLoginCount(loginCount);

            LoginRiskAssessResponse expectedResponse = createPassedResponse.apply("大陆IP默认通过");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("PASS"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }
    }

    // ========== Member Attribute Rule Tests ==========

    @Nested
    @DisplayName("Member Attribute Rules")
    class MemberAttributeTests {

        @Test
        @DisplayName("Should block frozen member with error code 40003")
        void shouldBlockFrozenMember() throws Exception {
            // Given - Frozen member
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setIsFrozenMember(true);

            LoginRiskAssessResponse expectedResponse = createRejectedResponse.apply("当前会员卡无法使用");
            expectedResponse.setAssessmentDetails("冻结会员被拦截");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("REJECT"))
                    .andExpect(jsonPath("$.blockMessage").value("当前会员卡无法使用"))
                    .andExpect(jsonPath("$.assessmentDetails").value("冻结会员被拦截"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }

        @Test
        @DisplayName("Should allow non-frozen member")
        void shouldAllowNonFrozenMember() throws Exception {
            // Given - Non-frozen member
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setIsFrozenMember(false);

            LoginRiskAssessResponse expectedResponse = createPassedResponse.apply("大陆IP默认通过");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("PASS"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }
    }

    // ========== Risk Control Validation Rule Tests ==========

    @Nested
    @DisplayName("Risk Control Validation Rules")
    class RiskControlValidationTests {

        @Test
        @DisplayName("Should block high risk with error code 40004 (silent blocking)")
        void shouldBlockHighRisk() throws Exception {
            // Given - High risk level
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setTxRiskLevel("高");

            LoginRiskAssessResponse expectedResponse = createRejectedResponse.apply("无提示，收不到验证码");
            expectedResponse.setAssessmentDetails("腾讯风控：高风险被拦截");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("REJECT"))
                    .andExpect(jsonPath("$.blockMessage").value("无提示，收不到验证码"))
                    .andExpect(jsonPath("$.assessmentDetails").value("腾讯风控：高风险被拦截"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }

        @Test
        @DisplayName("Should block medium risk SMS verification but not login request")
        void shouldBlockMediumRiskSmsVerification() throws Exception {
            // Given - Medium risk level
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setTxRiskLevel("中");

            LoginRiskAssessResponse expectedResponse = createRejectedResponse.apply("无提示，收不到验证码");
            expectedResponse.setAssessmentDetails("腾讯风控：中风险手机验证码拦截");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("REJECT"))
                    .andExpect(jsonPath("$.blockMessage").value("无提示，收不到验证码"))
                    .andExpect(jsonPath("$.assessmentDetails").value("腾讯风控：中风险手机验证码拦截"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }

        @Test
        @DisplayName("Should allow low risk level")
        void shouldAllowLowRiskLevel() throws Exception {
            // Given - Low risk level
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setTxRiskLevel("低");

            LoginRiskAssessResponse expectedResponse = createPassedResponse.apply("大陆IP默认通过");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("PASS"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }
    }

    // ========== Edge Cases and Boundary Condition Tests ==========

    @Nested
    @DisplayName("Edge Cases and Boundary Conditions")
    class EdgeCasesAndBoundaryTests {

        @Test
        @DisplayName("Should handle multiple rule violations and return all triggered rules")
        void shouldHandleMultipleRuleViolations() throws Exception {
            // Given - Multiple violations: virtual phone + frozen member + high risk
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setPhoneNumber("17012345678"); // Virtual number
            request.setIsFrozenMember(true); // Frozen member
            request.setTxRiskLevel("高"); // High risk

            // Expect the highest priority rule to be triggered (virtual phone has salience 90)
            LoginRiskAssessResponse expectedResponse = createRejectedResponse.apply("无提示，收不到验证码");
            expectedResponse.setAssessmentDetails("登录：虚拟号段被拦截");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("REJECT"))
                    .andExpect(jsonPath("$.blockMessage").value("无提示，收不到验证码"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }

        @Test
        @DisplayName("Should handle boundary value for UnionID login count (exactly 5)")
        void shouldHandleBoundaryValueForUnionIdLoginCount() throws Exception {
            // Given - Exactly 5 logins (should pass)
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setUnionIdLoginCount(5);

            LoginRiskAssessResponse expectedResponse = createPassedResponse.apply("大陆IP默认通过");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("PASS"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }

        @Test
        @DisplayName("Should handle boundary value for UnionID login count (exactly 6)")
        void shouldHandleBoundaryValueForUnionIdLoginCountExceeded() throws Exception {
            // Given - Exactly 6 logins (should be rejected)
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setUnionIdLoginCount(6);

            LoginRiskAssessResponse expectedResponse = createRejectedResponse
                    .apply("此微信登录会员账户已超过限额，您可登录近期使用过的会员账户");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("REJECT"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }

        @Test
        @DisplayName("Should handle null UnionID login count")
        void shouldHandleNullUnionIdLoginCount() throws Exception {
            // Given - Null UnionID login count
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setUnionIdLoginCount(null);

            LoginRiskAssessResponse expectedResponse = createPassedResponse.apply("大陆IP默认通过");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("PASS"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }

        @ParameterizedTest
        @DisplayName("Should handle various mall codes correctly")
        @CsvSource({
                "JAKC, PASS",
                "HKC, PASS",
                "KP, PASS",
                "ALL, PASS"
        })
        void shouldHandleVariousMallCodes(String mallCode, String expectedResult) throws Exception {
            // Given
            LoginRiskAssessRequest request = createBaseRequest.apply(mallCode);

            LoginRiskAssessResponse expectedResponse;
            if (expectedResult.equals("PASS")) {
                expectedResponse = createPassedResponse.apply("大陆IP默认通过");
            } else {
                expectedResponse = createRejectedResponse.apply("默认拒绝");
            }

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value(expectedResult));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }

        @Test
        @DisplayName("Should handle empty phone number")
        void shouldHandleEmptyPhoneNumber() throws Exception {
            // Given - Empty phone number
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setPhoneNumber("");

            LoginRiskAssessResponse expectedResponse = createPassedResponse.apply("大陆IP默认通过");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("PASS"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }

        @Test
        @DisplayName("Should handle null phone number")
        void shouldHandleNullPhoneNumber() throws Exception {
            // Given - Null phone number
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setPhoneNumber(null);

            LoginRiskAssessResponse expectedResponse = createPassedResponse.apply("大陆IP默认通过");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("PASS"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }
    }

    // ========== Positive Test Cases ==========

    @Nested
    @DisplayName("Positive Test Cases - Login Should Be Allowed")
    class PositiveTestCases {

        @Test
        @DisplayName("Should allow login with all valid inputs")
        void shouldAllowLoginWithAllValidInputs() throws Exception {
            // Given - All valid inputs
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setPhoneNumber("13800138000"); // Normal phone
            request.setUnionIdLoginCount(3); // Below threshold
            request.setIsFrozenMember(false); // Not frozen
            request.setTxRiskLevel("低"); // Low risk

            LoginRiskAssessResponse expectedResponse = createPassedResponse.apply("大陆IP默认通过");
            expectedResponse.setIpGeolocation(
                    new IpGeolocationInfo("China", "CN", "Beijing", "Beijing", true));

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("PASS"))
                    .andExpect(jsonPath("$.assessmentDetails").value("大陆IP默认通过"))
                    .andExpect(jsonPath("$.blockMessage").doesNotExist())
                    .andExpect(jsonPath("$.ipGeolocation.country").value("China"))
                    .andExpect(jsonPath("$.ipGeolocation.countryIsoCode").value("CN"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }

        @Test
        @DisplayName("Should allow login for non-mainland IP in login scenario")
        void shouldAllowLoginForNonMainlandIp() throws Exception {
            // Given - Non-mainland IP (should pass for login scenario)
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setIpAddress("*******"); // Non-mainland IP

            LoginRiskAssessResponse expectedResponse = createPassedResponse.apply("登录场景：非大陆IP允许访问");
            expectedResponse.setIpGeolocation(
                    new IpGeolocationInfo("United States", "US", "California", "Mountain View", true));

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("PASS"))
                    .andExpect(jsonPath("$.assessmentDetails").value("登录场景：非大陆IP允许访问"))
                    .andExpect(jsonPath("$.ipGeolocation.country").value("United States"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }

        @ParameterizedTest
        @DisplayName("Should allow login for various normal phone number prefixes")
        @ValueSource(strings = {"138", "139", "150", "151", "180", "181", "189"})
        void shouldAllowLoginForNormalPhonePrefixes(String prefix) throws Exception {
            // Given - Normal phone number prefixes
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setPhoneNumber(prefix + "12345678");

            LoginRiskAssessResponse expectedResponse = createPassedResponse.apply("大陆IP默认通过");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("PASS"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }

        @Test
        @DisplayName("Should allow login with minimal required fields")
        void shouldAllowLoginWithMinimalRequiredFields() throws Exception {
            // Given - Only required mall code
            LoginRiskAssessRequest request = new LoginRiskAssessRequest();
            request.setMallCode("ALL");
            // All other fields are null/default

            LoginRiskAssessResponse expectedResponse = createPassedResponse.apply("默认拒绝：未匹配到任何通过规则");
            expectedResponse.setRiskResult(RiskResult.REJECT); // Actually should be rejected due to default rule

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("REJECT"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }

        @Test
        @DisplayName("Should allow login with zero member points")
        void shouldAllowLoginWithZeroMemberPoints() throws Exception {
            // Given - Zero member points (not negative)
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setMemberPoints(0);

            LoginRiskAssessResponse expectedResponse = createPassedResponse.apply("大陆IP默认通过");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("PASS"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }

        @Test
        @DisplayName("Should allow login with high member points")
        void shouldAllowLoginWithHighMemberPoints() throws Exception {
            // Given - High member points
            LoginRiskAssessRequest request = createBaseRequest.apply("ALL");
            request.setMemberPoints(999999);

            LoginRiskAssessResponse expectedResponse = createPassedResponse.apply("大陆IP默认通过");

            when(loginRiskAssessmentService.assessLoginRisk(any(LoginRiskAssessRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.riskResult").value("PASS"));

            verify(loginRiskAssessmentService).assessLoginRisk(any(LoginRiskAssessRequest.class));
        }
    }

    // ========== Validation Tests ==========

    @Nested
    @DisplayName("Request Validation Tests")
    class ValidationTests {

        @Test
        @DisplayName("Should return 400 for missing mall code")
        void shouldReturn400ForMissingMallCode() throws Exception {
            // Given - Request without mall code
            LoginRiskAssessRequest request = new LoginRiskAssessRequest();
            // mallCode is null

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isBadRequest());

            verify(loginRiskAssessmentService, never()).assessLoginRisk(any());
        }

        @Test
        @DisplayName("Should return 400 for empty mall code")
        void shouldReturn400ForEmptyMallCode() throws Exception {
            // Given - Request with empty mall code
            LoginRiskAssessRequest request = new LoginRiskAssessRequest();
            request.setMallCode("");

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isBadRequest());

            verify(loginRiskAssessmentService, never()).assessLoginRisk(any());
        }

        @Test
        @DisplayName("Should return 400 for invalid JSON")
        void shouldReturn400ForInvalidJson() throws Exception {
            // Given - Invalid JSON
            String invalidJson = "{ invalid json }";

            // When & Then
            mockMvc.perform(post("/risk/login-assessment")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(invalidJson))
                    .andExpect(status().isBadRequest());

            verify(loginRiskAssessmentService, never()).assessLoginRisk(any());
        }
    }
}
