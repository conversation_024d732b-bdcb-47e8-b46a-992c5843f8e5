package com.kerryprops.kip.riskcontrol.parking;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for parking benefits risk assessment.
 */
@Slf4j
@RestController
@RequestMapping("/api/risk-assessment/parking-benefits")
@RequiredArgsConstructor
@ConditionalOnProperty(name = "drools.enabled", havingValue = "true", matchIfMissing = true)
@Tag(name = "Parking Benefits Risk Assessment", description = "Parking benefits risk assessment endpoints")
@Hidden
public class ParkingBenefitRiskAssessmentController {

    private final ParkingBenefitRiskAssessmentService parkingBenefitRiskAssessmentService;

    @Operation(
            summary = "Assess parking benefits risk",
            description = "Assess risk for parking benefits scenario including IP location and member status checks"
    )
    @PostMapping
    public ParkingBenefitRiskAssessResponse assessParkingBenefitRisk(
            @Valid @RequestBody ParkingBenefitRiskAssessRequest request) {
        log.info("Received parking benefit risk assessment request - mall: {}, IP: {}",
                request.getMallCode(), request.getIpAddress());

        ParkingBenefitRiskAssessResponse response = parkingBenefitRiskAssessmentService.assessParkingBenefitRisk(request);

        log.info("Completed parking benefit risk assessment - mall: {}, result: {}",
                request.getMallCode(), response.getRiskResult());

        return response;
    }
}
