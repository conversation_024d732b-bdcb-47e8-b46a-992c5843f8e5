package com.kerryprops.kip.riskcontrol;

import com.kerryprops.kip.exception.BadRequestException;
import com.kerryprops.kip.riskcontrol.general.*;
import com.kerryprops.kip.riskcontrol.login.LoginRiskAssessRequest;
import com.kerryprops.kip.riskcontrol.login.LoginRiskAssessResponse;
import com.kerryprops.kip.riskcontrol.login.LoginRiskAssessmentService;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Controller for risk assessment API endpoints.
 * Provides comprehensive risk assessment capabilities including phone number validation,
 * IP geolocation-based assessment, and Tencent Cloud risk evaluation.
 *
 * <p>For detailed documentation,
 * see: <a href="https://kerryprops.atlassian.net/wiki/spaces/TAIC/pages/675840071/S+-">...</a>
 */
@Slf4j
@RestController
@Validated
@RequestMapping("/risk")
@RequiredArgsConstructor
@Tag(name = "Kerry风险评估", description = "Kerry Properties风险评估服务，包括手机号风险评估、IP地址地理位置风险评估和腾讯云风控服务")
public class RiskAssessmentController {

    private final RiskAssessmentService riskAssessmentService;
    private final TencentCloudService tencentCloudService;
    private final LoginRiskAssessmentService loginRiskAssessmentService;

    @Operation(
            summary = "Assess login risk",
            description = """
                    登录场景风控规则（优先级排序）
                                      1. IP归属地规则
                                      规则说明：非中国大陆IP在登录场景不作拦截
                                      适用条件：用户IP为非中国大陆IP
                                      拦截逻辑：仅在部分业务场景拦截（登录场景不拦截）
                                      适用范围：不适用登录场景
                                      2. 手机号段规则
                                      非大陆手机号
                                      规则说明：非中国大陆手机号仅在特定场景拦截
                                      适用条件：手机号国际码非+86
                                      拦截逻辑：仅在拍照积分场景拦截（登录场景不拦截）
                                      适用范围：拍照积分场景
                                      虚拟号段（登录拦截）
                                      规则说明：拦截所有使用虚拟号段的登录请求
                                      触发条件：手机号属于虚拟运营商号段
                                      拦截动作：禁止登录且不发送验证码
                                      提示代码：40001（前端需处理无提示场景）
                                      提示信息：无（静默拦截）
                                      适用范围：全部商场（ALL）
                                      3. 积分规则
                                      规则说明：负积分仅在特定场景拦截
                                      适用条件：账户积分为负值
                                      拦截逻辑：仅在停车/积分场景拦截（登录场景不拦截）
                                      适用范围：停车/积分场景
                                      4. ID校验规则（登录拦截）
                                      规则说明：防止账号滥用
                                      触发条件：同一UnionID 1个月内接收登录短信验证码使用的手机号码个数>3
                                      拦截动作：禁止登录
                                      提示代码：40002
                                      提示信息："此微信登录会员账户已超过限额，您可登录近期使用过的会员账户"
                                      适用范围：全部商场（ALL）
                                      5. 会员属性规则（登录拦截）
                                      规则说明：限制冻结账户的登录
                                      触发条件：会员状态=冻结
                                      拦截动作：禁止登录
                                      提示代码：40003
                                      提示信息："当前会员卡无法使用"
                                      适用范围：全部商场（ALL）
                                      6. 风控校验规则
                                      高风险拦截
                                      规则说明：腾讯风控高风险实时拦截
                                      触发条件：腾讯风控等级=高
                                      拦截动作：禁止登录且不发送验证码
                                      提示代码：40004（前端需处理无提示场景）
                                      提示信息：无（静默拦截）
                                      适用范围：全部商场（ALL）
                                      中风险拦截
                                      规则说明：腾讯风控中风险验证码拦截
                                      触发条件：腾讯风控等级=中
                                      拦截动作：拦截短信验证码（不阻止登录请求）
                                      提示代码：无
                                      提示信息：无
                                      适用范围：全部商场（ALL）
                                      登录专用响应码
                                      错误码	触发规则	用户提示信息
                                      40001	虚拟号段	无（前端需处理无提示场景）
                                      40002	ID校验超标	"此微信登录会员账户已超过限额，您可登录近期使用过的会员账户"
                                      40003	账户冻结	"当前会员卡无法使用"
                                      40004	风控高风险	无（前端需处理无提示场景）
                    """
    )
    @PostMapping("/login-assessment")
    public LoginRiskAssessResponse assessLoginRisk(@Valid @RequestBody LoginRiskAssessRequest request) {
        log.info("Received login risk assessment request - mall: {}, IP: {}",
                request.getMallCode(), request.getIpAddress());

        LoginRiskAssessResponse response = loginRiskAssessmentService.assessLoginRisk(request);

        log.info("Completed login risk assessment - mall: {}, result: {}",
                request.getMallCode(), response.getRiskResult());

        return response;
    }

    /**
     * Endpoint to assess risk for phone numbers.
     *
     * @param request       the request containing phone numbers to assess
     * @param bindingResult validation result
     * @return response entity with assessment results or error
     */
    @Operation(summary = "批量虚拟手机号风险评估", description = "通过是否高危虚拟号评估风险")
    @PostMapping("/assess")
    public List<PhoneRiskAssessResponse> assessPhoneNumbers(@Valid @NotNull @RequestBody PhoneRiskAssessRequest request,
                                                            BindingResult bindingResult) {

        log.info("Received risk assessment request for {} phone numbers", Optional.of(request.getPhoneNumbers())
                .map(List::size)
                .orElse(0));

        // Validate request for NotEmpty
        if (bindingResult.hasErrors()) {
            var errorMessage = bindingResult.getAllErrors()
                    .stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .filter(Objects::nonNull)
                    .findFirst()
                    .orElse("Invalid request");

            log.warn("Validation error: {}", errorMessage);
            throw new BadRequestException();
        }

        // Validate phone numbers according to the new pattern
        List<PhoneRiskAssessResponse> results = new ArrayList<>();
        for (String originPhoneNumber : request.getPhoneNumbers()) {
            String phoneNumber = StringUtils.deleteWhitespace(originPhoneNumber);
            if (isValidPhoneNumber(phoneNumber)) {
                // Process valid phone numbers with the service
                results.add(riskAssessmentService.assessPhoneNumber(phoneNumber));
                log.info("Processing valid phone number: {}", phoneNumber);
            } else {
                // Return LOW risk level for all invalid phone numbers
                // This includes numbers with different lengths or not starting with 1
                results.add(createLowRiskResponse(phoneNumber));
                log.warn("Invalid phone number format: {}, returning LOW risk level", phoneNumber);
            }
        }

        log.info("Successfully processed risk assessment for {} phone numbers", results.size());
        return results;
    }

    @Operation(summary = "腾讯云风控", description = "通过腾讯风控引擎对用户信息进行风控评估")
    @PostMapping("/tencent-assess")
    public TxRiskResponse assessPhoneNumber(@Valid @RequestBody PhoneTxRiskAssessRequest request)
            throws TencentCloudSDKException {
        return tencentCloudService.assessPhoneRisk(request);
    }

    /**
     * Validates if a phone number matches the required pattern.
     * Valid phone numbers must:
     * 1. Have an optional +86 or 86 prefix (+ is optional in +86)
     * 2. Be 11 digits without prefix
     * 3. Start with 1 as the first digit of the 11-digit number
     *
     * @param phoneNumber the phone number to validate
     * @return true if the phone number is valid, false otherwise
     */
    private boolean isValidPhoneNumber(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber)) {
            return false;
        }
        return PhoneNumberUtil.PHONE_PATTERN.matcher(phoneNumber)
                .matches();
    }

    /**
     * Creates a response with LOW risk level for invalid phone numbers.
     * Handles empty or null phone numbers by using empty string as a default.
     *
     * @param phoneNumber the phone number, can be null or empty
     * @return the risk assessment response with LOW risk
     */
    private PhoneRiskAssessResponse createLowRiskResponse(String phoneNumber) {
        // Use empty string if phoneNumber is null to avoid NullPointerException
        String safePhoneNumber = phoneNumber != null ? phoneNumber : "";

        return new PhoneRiskAssessResponse(safePhoneNumber, RiskLevel.LOW, Collections.singletonList(RiskType.NONE),
                LocalDateTime.now());
    }

}
