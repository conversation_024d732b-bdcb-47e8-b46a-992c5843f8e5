package com.kerryprops.kip.riskcontrol.photopoints;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for photo points risk assessment.
 */
@Slf4j
@RestController
@RequestMapping("/api/risk-assessment/photo-points")
@RequiredArgsConstructor
@ConditionalOnProperty(name = "drools.enabled", havingValue = "true", matchIfMissing = true)
@Tag(name = "Photo Points Risk Assessment", description = "Photo points risk assessment endpoints")
@Hidden
public class PhotoPointsRiskAssessmentController {

    private final PhotoPointsRiskAssessmentService photoPointsRiskAssessmentService;

    @Operation(
            summary = "Assess photo points risk",
            description = "Assess risk for photo points scenario including IP location and phone number validation"
    )
    @PostMapping
    public PhotoPointsRiskAssessResponse assessPhotoPointsRisk(@Valid @RequestBody PhotoPointsRiskAssessRequest request) {
        log.info("Received photo points risk assessment request - mall: {}, IP: {}, phone: {}",
                request.getMallCode(), request.getIpAddress(), request.getPhoneNumber());

        PhotoPointsRiskAssessResponse response = photoPointsRiskAssessmentService.assessPhotoPointsRisk(request);

        log.info("Completed photo points risk assessment - mall: {}, result: {}",
                request.getMallCode(), response.getRiskResult());

        return response;
    }
}
