package com.kerryprops.kip.riskcontrol.rules;

import com.kerryprops.kip.riskcontrol.shared.RiskAssessmentContext;
import com.kerryprops.kip.riskcontrol.general.RiskResult;

/**
 * Risk assessment rules specific to SALES_POINTS business scenario.
 * 
 * Business Rules:
 * - 负积分 -> N (拦截)
 *
 * <AUTHOR> Properties Limited
 * @since 1.0
 */

// ========== 积分相关规则 ==========

// 销售积分：负积分 -> N (拦截)
rule "Sales Points - Negative Points - REJECT"
    salience 80
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.SALES_POINTS,
            memberPoints != null,
            memberPoints < 0,
            mallCode == RiskAssessmentContext.MallCode.JAKC || mallCode == RiskAssessmentContext.MallCode.HKC || mallCode == RiskAssessmentContext.MallCode.KP,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setAssessmentDetails("销售积分：负积分被拦截");
        System.out.println("Rule executed: Sales Points - Negative Points - REJECT");
end
